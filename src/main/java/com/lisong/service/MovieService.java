package com.lisong.service;

import com.lisong.entity.Movie;
import com.lisong.entity.MovieListData;
import com.lisong.entity.CategoryData;

/**
 * 电影服务接口
 */
public interface MovieService {
    
    /**
     * 获取最新电影列表
     * @param page 页码
     * @param limit 每页数量
     * @param genre 类型筛选
     * @param year 年份筛选
     * @param region 地区筛选
     * @param sort 排序方式
     * @return 电影列表数据
     */
    MovieListData getLatestMovies(Integer page, Integer limit, String genre, Integer year, String region, String sort);
    
    /**
     * 获取电影分类
     * @return 分类数据
     */
    CategoryData getMovieCategories();
    
    /**
     * 获取电影详情
     * @param id 电影ID
     * @return 电影详情
     */
    Movie getMovieDetail(Long id);
}
