package com.lisong.service;

import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;

/**
 * 电视剧服务接口
 */
public interface TvSeriesService {
    
    /**
     * 获取电视剧列表
     * @param page 页码
     * @param limit 每页数量
     * @param genre 类型筛选
     * @param region 地区筛选
     * @param status 状态筛选
     * @param sort 排序方式
     * @return 电视剧列表数据
     */
    TvSeriesListData getTvSeriesList(Integer page, Integer limit, String genre, String region, String status, String sort);
    
    /**
     * 获取电视剧详情
     * @param id 电视剧ID
     * @return 电视剧详情
     */
    TvSeries getTvSeriesDetail(Long id);
}
