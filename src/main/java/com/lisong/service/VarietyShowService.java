package com.lisong.service;

import com.lisong.entity.VarietyShow;
import com.lisong.entity.VarietyShowListData;

/**
 * 综艺节目服务接口
 */
public interface VarietyShowService {
    
    /**
     * 获取综艺节目列表
     * @param page 页码
     * @param limit 每页数量
     * @param genre 类型筛选
     * @param region 地区筛选
     * @param status 状态筛选
     * @param sort 排序方式
     * @return 综艺节目列表数据
     */
    VarietyShowListData getVarietyShowsList(Integer page, Integer limit, String genre, String region, String status, String sort);
    
    /**
     * 获取综艺节目详情
     * @param id 综艺节目ID
     * @return 综艺节目详情
     */
    VarietyShow getVarietyShowDetail(Long id);
}
