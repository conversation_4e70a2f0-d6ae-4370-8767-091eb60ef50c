package com.lisong.service.impl;

import com.lisong.entity.HomeData;
import com.lisong.entity.Carousel;
import com.lisong.entity.Movie;
import com.lisong.entity.Stats;
import com.lisong.mapper.CarouselMapper;
import com.lisong.mapper.MovieMapper;
import com.lisong.mapper.StatsMapper;
import com.lisong.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 首页服务实现
 */
@Service
public class HomeServiceImpl implements HomeService {
    
    @Autowired
    private CarouselMapper carouselMapper;
    
    @Autowired
    private MovieMapper movieMapper;
    
    @Autowired
    private StatsMapper statsMapper;
    
    @Override
    public HomeData getHomeData() {
        // 获取轮播图数据
        List<Carousel> carousels = carouselMapper.findActiveCarousels();
        
        // 获取热门电影
        List<Movie> hotMovies = movieMapper.findHotMovies();
        
        // 获取最新电影
        List<Movie> latestMovies = movieMapper.findLatestMovies();
        
        // 获取统计数据
        Stats stats = new Stats();
        stats.setTotalMovies(statsMapper.countMovies());
        stats.setTotalTVShows(statsMapper.countTVShows());
        stats.setTotalVarietyShows(statsMapper.countVarietyShows());
        stats.setTotalUsers(statsMapper.countUsers());
        
        // 封装首页数据
        HomeData homeData = new HomeData();
        homeData.setCarousel(carousels);
        homeData.setHotMovies(hotMovies);
        homeData.setLatestMovies(latestMovies);
        homeData.setStats(stats);
        
        return homeData;
    }
}
