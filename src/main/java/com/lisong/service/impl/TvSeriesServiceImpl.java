package com.lisong.service.impl;

import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;
import com.lisong.entity.TvSeriesEpisode;
import com.lisong.entity.PaginationData;
import com.lisong.mapper.TvSeriesMapper;
import com.lisong.mapper.TvSeriesEpisodeMapper;
import com.lisong.service.TvSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 电视剧服务实现
 */
@Service
public class TvSeriesServiceImpl implements TvSeriesService {
    
    @Autowired
    private TvSeriesMapper tvSeriesMapper;
    
    @Autowired
    private TvSeriesEpisodeMapper tvSeriesEpisodeMapper;
    
    @Override
    public TvSeriesListData getTvSeriesList(Integer page, Integer limit, String genre, String region, String status, String sort) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }
        
        // 计算偏移量
        Integer offset = (page - 1) * limit;
        
        // 查询电视剧列表
        List<TvSeries> tvSeriesList = tvSeriesMapper.findTvSeriesWithFilter(genre, region, status, sort, offset, limit);
        
        // 查询总数
        Long total = tvSeriesMapper.countTvSeriesWithFilter(genre, region, status);
        
        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);
        
        return new TvSeriesListData(tvSeriesList, pagination);
    }
    
    @Override
    public TvSeries getTvSeriesDetail(Long id) {
        // 查询电视剧基本信息
        TvSeries tvSeries = tvSeriesMapper.findById(id);
        
        if (tvSeries != null) {
            // 查询剧集列表
            List<TvSeriesEpisode> episodes = tvSeriesEpisodeMapper.findBySeriesId(id);
            tvSeries.setEpisodes(episodes);
        }
        
        return tvSeries;
    }
}
