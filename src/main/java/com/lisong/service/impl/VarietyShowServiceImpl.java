package com.lisong.service.impl;

import com.lisong.entity.VarietyShow;
import com.lisong.entity.VarietyShowListData;
import com.lisong.entity.VarietyShowEpisode;
import com.lisong.entity.PaginationData;
import com.lisong.mapper.VarietyShowMapper;
import com.lisong.mapper.VarietyShowEpisodeMapper;
import com.lisong.service.VarietyShowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 综艺节目服务实现
 */
@Service
public class VarietyShowServiceImpl implements VarietyShowService {
    
    @Autowired
    private VarietyShowMapper varietyShowMapper;
    
    @Autowired
    private VarietyShowEpisodeMapper varietyShowEpisodeMapper;
    
    @Override
    public VarietyShowListData getVarietyShowsList(Integer page, Integer limit, String genre, String region, String status, String sort) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }
        
        // 计算偏移量
        Integer offset = (page - 1) * limit;
        
        // 查询综艺节目列表
        List<VarietyShow> varietyShowsList = varietyShowMapper.findVarietyShowsWithFilter(genre, region, status, sort, offset, limit);
        
        // 查询总数
        Long total = varietyShowMapper.countVarietyShowsWithFilter(genre, region, status);
        
        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);
        
        return new VarietyShowListData(varietyShowsList, pagination);
    }
    
    @Override
    public VarietyShow getVarietyShowDetail(Long id) {
        // 查询综艺节目基本信息
        VarietyShow varietyShow = varietyShowMapper.findById(id);
        
        if (varietyShow != null) {
            // 查询期数列表
            List<VarietyShowEpisode> episodes = varietyShowEpisodeMapper.findByShowId(id);
            varietyShow.setEpisodes(episodes);
        }
        
        return varietyShow;
    }
}
