package com.lisong.service.impl;

import com.lisong.entity.*;
import com.lisong.mapper.MovieMapper;
import com.lisong.mapper.MovieCategoryMapper;
import com.lisong.service.MovieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.List;

/**
 * 电影服务实现
 */
@Service
public class MovieServiceImpl implements MovieService {
    
    @Autowired
    private MovieMapper movieMapper;
    
    @Autowired
    private MovieCategoryMapper movieCategoryMapper;
    
    @Override
    public MovieListData getLatestMovies(Integer page, Integer limit, String genre, Integer year, String region, String sort) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }
        
        // 计算偏移量
        Integer offset = (page - 1) * limit;
        
        // 查询电影列表
        List<Movie> movies = movieMapper.findLatestMoviesWithFilter(genre, year, region, sort, offset, limit);
        
        // 查询总数
        Long total = movieMapper.countMoviesWithFilter(genre, year, region);
        
        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);
        
        return new MovieListData(movies, pagination);
    }
    
    @Override
    public CategoryData getMovieCategories() {
        // 获取所有分类
        List<MovieCategory> categories = movieCategoryMapper.findAllActiveCategories();
        
        // 为每个分类统计电影数量
        for (MovieCategory category : categories) {
            Long count = movieCategoryMapper.countMoviesByCategory(category.getName());
            category.setCount(count);
        }
        
        // 获取统计数据
        CategoryStats stats = new CategoryStats();
        stats.setTotalCategories(movieCategoryMapper.countCategories());
        stats.setTotalMovies(movieMapper.countMovies());
        stats.setMonthlyNew(movieMapper.countMonthlyNewMovies());
        
        Double avgRating = movieMapper.getAverageRating();
        stats.setAverageRating(avgRating != null ? BigDecimal.valueOf(avgRating) : BigDecimal.ZERO);
        
        return new CategoryData(categories, stats);
    }
    
    @Override
    public Movie getMovieDetail(Long id) {
        return movieMapper.findById(id);
    }
}
