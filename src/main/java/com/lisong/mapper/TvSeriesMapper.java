package com.lisong.mapper;

import com.lisong.entity.TvSeries;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 电视剧数据访问接口
 */
@Mapper
public interface TvSeriesMapper {
    
    /**
     * 根据条件查询电视剧列表（分页）
     */
    @Select("<script>" +
            "SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<if test='seriesStatus != null and seriesStatus != \"\"'>" +
            "AND series_status = #{seriesStatus} " +
            "</if>" +
            "<choose>" +
            "<when test='sort == \"rating\"'>" +
            "ORDER BY rating DESC, view_count DESC " +
            "</when>" +
            "<when test='sort == \"popular\"'>" +
            "ORDER BY view_count DESC, rating DESC " +
            "</when>" +
            "<when test='sort == \"name\"'>" +
            "ORDER BY title ASC " +
            "</when>" +
            "<otherwise>" +
            "ORDER BY created_at DESC, rating DESC " +
            "</otherwise>" +
            "</choose>" +
            "LIMIT #{limit} OFFSET #{offset}" +
            "</script>")
    List<TvSeries> findTvSeriesWithFilter(@Param("genre") String genre, 
                                         @Param("region") String region, 
                                         @Param("seriesStatus") String seriesStatus, 
                                         @Param("sort") String sort, 
                                         @Param("offset") Integer offset, 
                                         @Param("limit") Integer limit);
    
    /**
     * 根据条件统计电视剧数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM tv_series WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<if test='seriesStatus != null and seriesStatus != \"\"'>" +
            "AND series_status = #{seriesStatus} " +
            "</if>" +
            "</script>")
    Long countTvSeriesWithFilter(@Param("genre") String genre, 
                                @Param("region") String region, 
                                @Param("seriesStatus") String seriesStatus);
    
    /**
     * 根据ID查询电视剧详情
     */
    @Select("SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series WHERE id = #{id} AND status = 1")
    TvSeries findById(Long id);
    
    /**
     * 获取电视剧总数（用于统计）
     */
    @Select("SELECT COUNT(*) FROM tv_series WHERE status = 1")
    Long countTvSeries();
}
