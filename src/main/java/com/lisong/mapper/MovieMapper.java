package com.lisong.mapper;

import com.lisong.entity.Movie;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 电影数据访问接口
 */
@Mapper
public interface MovieMapper {
    
    /**
     * 获取热门电影列表
     */
    @Select("SELECT id, title, original_title as originalTitle, poster, backdrop, rating, movie_year as movieYear, duration, genre, region, director, \"cast\", description, release_date as releaseDate, box_office as boxOffice, trailer_url as trailerUrl, play_url as playUrl, movie_status as movieStatus, is_hot as isHot, is_latest as isLatest, view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt FROM movie WHERE status = 1 AND is_hot = 1 ORDER BY view_count DESC, rating DESC LIMIT 10")
    List<Movie> findHotMovies();

    /**
     * 获取最新电影列表
     */
    @Select("SELECT id, title, original_title as originalTitle, poster, backdrop, rating, movie_year as movieYear, duration, genre, region, director, \"cast\", description, release_date as releaseDate, box_office as boxOffice, trailer_url as trailerUrl, play_url as playUrl, movie_status as movieStatus, is_hot as isHot, is_latest as isLatest, view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt FROM movie WHERE status = 1 AND is_latest = 1 ORDER BY created_at DESC LIMIT 10")
    List<Movie> findLatestMovies();
    
    /**
     * 根据ID查询电影
     */
    @Select("SELECT id, title, original_title as originalTitle, poster, backdrop, rating, movie_year as movieYear, duration, genre, region, director, \"cast\", description, release_date as releaseDate, box_office as boxOffice, trailer_url as trailerUrl, play_url as playUrl, movie_status as movieStatus, is_hot as isHot, is_latest as isLatest, view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt FROM movie WHERE id = #{id}")
    Movie findById(Long id);

    /**
     * 获取电影总数
     */
    @Select("SELECT COUNT(*) FROM movie WHERE status = 1")
    Long countMovies();

    /**
     * 获取所有电影
     */
    @Select("SELECT id, title, original_title as originalTitle, poster, backdrop, rating, movie_year as movieYear, duration, genre, region, director, \"cast\", description, release_date as releaseDate, box_office as boxOffice, trailer_url as trailerUrl, play_url as playUrl, movie_status as movieStatus, is_hot as isHot, is_latest as isLatest, view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt FROM movie WHERE status = 1 ORDER BY created_at DESC")
    List<Movie> findAll();

    /**
     * 分页查询最新电影，支持筛选和排序
     */
    @Select("<script>" +
            "SELECT id, title, original_title as originalTitle, poster, backdrop, rating, movie_year as movieYear, duration, genre, region, director, \"cast\", description, release_date as releaseDate, box_office as boxOffice, trailer_url as trailerUrl, play_url as playUrl, movie_status as movieStatus, is_hot as isHot, is_latest as isLatest, view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM movie WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='year != null'>" +
            "AND movie_year = #{year} " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<choose>" +
            "<when test='sort == \"rating\"'>ORDER BY rating DESC</when>" +
            "<when test='sort == \"popular\"'>ORDER BY view_count DESC</when>" +
            "<when test='sort == \"name\"'>ORDER BY title ASC</when>" +
            "<otherwise>ORDER BY created_at DESC</otherwise>" +
            "</choose>" +
            " LIMIT #{offset}, #{limit}" +
            "</script>")
    List<Movie> findLatestMoviesWithFilter(@Param("genre") String genre,
                                          @Param("year") Integer year,
                                          @Param("region") String region,
                                          @Param("sort") String sort,
                                          @Param("offset") Integer offset,
                                          @Param("limit") Integer limit);

    /**
     * 统计符合条件的电影总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM movie WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='year != null'>" +
            "AND movie_year = #{year} " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "</script>")
    Long countMoviesWithFilter(@Param("genre") String genre,
                              @Param("year") Integer year,
                              @Param("region") String region);

    /**
     * 获取本月新增电影数量
     */
    @Select("SELECT COUNT(*) FROM movie WHERE status = 1 AND YEAR(created_at) = YEAR(CURRENT_DATE) AND MONTH(created_at) = MONTH(CURRENT_DATE)")
    Long countMonthlyNewMovies();

    /**
     * 获取电影平均评分
     */
    @Select("SELECT AVG(rating) FROM movie WHERE status = 1 AND rating > 0")
    Double getAverageRating();
}
