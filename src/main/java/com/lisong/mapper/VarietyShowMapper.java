package com.lisong.mapper;

import com.lisong.entity.VarietyShow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 综艺节目数据访问接口
 */
@Mapper
public interface VarietyShowMapper {
    
    /**
     * 根据条件查询综艺节目列表（分页）
     */
    @Select("<script>" +
            "SELECT id, title, poster, backdrop, rating, show_year as showYear, genre, region, description, " +
            "show_status as showStatus, update_day as updateDay, update_time as updateTime, season, hosts, guests, " +
            "first_air_date as firstAirDate, view_count as viewCount, like_count as likeCount, " +
            "status, created_at as createdAt, updated_at as updatedAt " +
            "FROM variety_show WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<if test='showStatus != null and showStatus != \"\"'>" +
            "AND show_status = #{showStatus} " +
            "</if>" +
            "<choose>" +
            "<when test='sort == \"rating\"'>" +
            "ORDER BY rating DESC, view_count DESC " +
            "</when>" +
            "<when test='sort == \"popular\"'>" +
            "ORDER BY view_count DESC, rating DESC " +
            "</when>" +
            "<when test='sort == \"name\"'>" +
            "ORDER BY title ASC " +
            "</when>" +
            "<otherwise>" +
            "ORDER BY created_at DESC, rating DESC " +
            "</otherwise>" +
            "</choose>" +
            "LIMIT #{limit} OFFSET #{offset}" +
            "</script>")
    List<VarietyShow> findVarietyShowsWithFilter(@Param("genre") String genre, 
                                                 @Param("region") String region, 
                                                 @Param("showStatus") String showStatus, 
                                                 @Param("sort") String sort, 
                                                 @Param("offset") Integer offset, 
                                                 @Param("limit") Integer limit);
    
    /**
     * 根据条件统计综艺节目数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM variety_show WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<if test='showStatus != null and showStatus != \"\"'>" +
            "AND show_status = #{showStatus} " +
            "</if>" +
            "</script>")
    Long countVarietyShowsWithFilter(@Param("genre") String genre, 
                                    @Param("region") String region, 
                                    @Param("showStatus") String showStatus);
    
    /**
     * 根据ID查询综艺节目详情
     */
    @Select("SELECT id, title, poster, backdrop, rating, show_year as showYear, genre, region, description, " +
            "show_status as showStatus, update_day as updateDay, update_time as updateTime, season, hosts, guests, " +
            "first_air_date as firstAirDate, view_count as viewCount, like_count as likeCount, " +
            "status, created_at as createdAt, updated_at as updatedAt " +
            "FROM variety_show WHERE id = #{id} AND status = 1")
    VarietyShow findById(Long id);
    
    /**
     * 获取综艺节目总数（用于统计）
     */
    @Select("SELECT COUNT(*) FROM variety_show WHERE status = 1")
    Long countVarietyShows();
}
