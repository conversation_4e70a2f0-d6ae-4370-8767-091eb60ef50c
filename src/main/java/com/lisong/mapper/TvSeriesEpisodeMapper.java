package com.lisong.mapper;

import com.lisong.entity.TvSeriesEpisode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 电视剧剧集数据访问接口
 */
@Mapper
public interface TvSeriesEpisodeMapper {
    
    /**
     * 根据电视剧ID查询剧集列表
     */
    @Select("SELECT id, series_id as seriesId, episode_number as episodeNumber, title, duration, play_url as playUrl, " +
            "status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series_episode WHERE series_id = #{seriesId} AND status = 1 " +
            "ORDER BY episode_number ASC")
    List<TvSeriesEpisode> findBySeriesId(@Param("seriesId") Long seriesId);
    
    /**
     * 根据ID查询剧集
     */
    @Select("SELECT id, series_id as seriesId, episode_number as episodeNumber, title, duration, play_url as playUrl, " +
            "status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series_episode WHERE id = #{id} AND status = 1")
    TvSeriesEpisode findById(Long id);
}
