package com.lisong.mapper;

import com.lisong.entity.MovieCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 电影分类数据访问接口
 */
@Mapper
public interface MovieCategoryMapper {
    
    /**
     * 获取所有启用的电影分类
     */
    @Select("SELECT id, name, description, image, badge, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM movie_category WHERE status = 1 ORDER BY sort_order ASC")
    List<MovieCategory> findAllActiveCategories();
    
    /**
     * 根据ID查询分类
     */
    @Select("SELECT id, name, description, image, badge, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM movie_category WHERE id = #{id}")
    MovieCategory findById(String id);
    
    /**
     * 获取分类总数
     */
    @Select("SELECT COUNT(*) FROM movie_category WHERE status = 1")
    Long countCategories();
    
    /**
     * 获取指定分类下的电影数量
     */
    @Select("SELECT COUNT(*) FROM movie WHERE status = 1 AND genre LIKE CONCAT('%', #{categoryName}, '%')")
    Long countMoviesByCategory(String categoryName);
}
