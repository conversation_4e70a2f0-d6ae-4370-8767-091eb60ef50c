package com.lisong.mapper;

import com.lisong.entity.Movie;
import com.lisong.entity.TvSeries;
import com.lisong.entity.VarietyShow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 搜索相关的数据访问层
 */
@Mapper
public interface SearchMapper {
    
    /**
     * 搜索电影
     */
    @Select("SELECT * FROM movie WHERE status = 1 AND " +
            "(title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "original_title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "director LIKE CONCAT('%', #{keyword}, '%') OR " +
            "\"cast\" LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY rating DESC, view_count DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<Movie> searchMovies(@Param("keyword") String keyword,
                            @Param("limit") int limit,
                            @Param("offset") int offset);

    /**
     * 统计电影搜索结果数量
     */
    @Select("SELECT COUNT(*) FROM movie WHERE status = 1 AND " +
            "(title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "original_title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "director LIKE CONCAT('%', #{keyword}, '%') OR " +
            "\"cast\" LIKE CONCAT('%', #{keyword}, '%'))")
    long countMovies(@Param("keyword") String keyword);
    
    /**
     * 搜索电视剧
     */
    @Select("SELECT * FROM tv_series WHERE status = 1 AND " +
            "(title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "director LIKE CONCAT('%', #{keyword}, '%') OR " +
            "\"cast\" LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY rating DESC, view_count DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<TvSeries> searchTvSeries(@Param("keyword") String keyword,
                                 @Param("limit") int limit,
                                 @Param("offset") int offset);

    /**
     * 统计电视剧搜索结果数量
     */
    @Select("SELECT COUNT(*) FROM tv_series WHERE status = 1 AND " +
            "(title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "director LIKE CONCAT('%', #{keyword}, '%') OR " +
            "\"cast\" LIKE CONCAT('%', #{keyword}, '%'))")
    long countTvSeries(@Param("keyword") String keyword);
    
    /**
     * 搜索综艺节目
     */
    @Select("SELECT * FROM variety_show WHERE status = 1 AND " +
            "(title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "hosts LIKE CONCAT('%', #{keyword}, '%') OR " +
            "guests LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY rating DESC, view_count DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<VarietyShow> searchVarietyShows(@Param("keyword") String keyword, 
                                        @Param("limit") int limit, 
                                        @Param("offset") int offset);
    
    /**
     * 统计综艺节目搜索结果数量
     */
    @Select("SELECT COUNT(*) FROM variety_show WHERE status = 1 AND " +
            "(title LIKE CONCAT('%', #{keyword}, '%') OR " +
            "hosts LIKE CONCAT('%', #{keyword}, '%') OR " +
            "guests LIKE CONCAT('%', #{keyword}, '%'))")
    long countVarietyShows(@Param("keyword") String keyword);
    
    /**
     * 获取电影标题建议
     */
    @Select("SELECT title FROM movie WHERE status = 1 AND " +
            "title LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY view_count DESC " +
            "LIMIT #{limit}")
    List<String> getMovieTitleSuggestions(@Param("keyword") String keyword,
                                         @Param("limit") int limit);

    /**
     * 获取电视剧标题建议
     */
    @Select("SELECT title FROM tv_series WHERE status = 1 AND " +
            "title LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY view_count DESC " +
            "LIMIT #{limit}")
    List<String> getTvSeriesTitleSuggestions(@Param("keyword") String keyword,
                                           @Param("limit") int limit);

    /**
     * 获取综艺节目标题建议
     */
    @Select("SELECT title FROM variety_show WHERE status = 1 AND " +
            "title LIKE CONCAT('%', #{keyword}, '%') " +
            "ORDER BY view_count DESC " +
            "LIMIT #{limit}")
    List<String> getVarietyShowTitleSuggestions(@Param("keyword") String keyword,
                                              @Param("limit") int limit);
}
