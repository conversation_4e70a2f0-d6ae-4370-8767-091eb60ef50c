package com.lisong.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 统计数据访问接口
 */
@Mapper
public interface StatsMapper {
    
    /**
     * 获取电影总数
     */
    @Select("SELECT COUNT(*) FROM movie WHERE status = 1")
    Long countMovies();
    
    /**
     * 获取电视剧总数
     */
    @Select("SELECT COUNT(*) FROM tv_series WHERE status = 1")
    Long countTVShows();
    
    /**
     * 获取综艺节目总数
     */
    @Select("SELECT COUNT(*) FROM variety_show WHERE status = 1")
    Long countVarietyShows();
    
    /**
     * 获取用户总数
     */
    @Select("SELECT COUNT(*) FROM \"user\" WHERE status = 1")
    Long countUsers();
}
