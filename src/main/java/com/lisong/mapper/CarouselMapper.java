package com.lisong.mapper;

import com.lisong.entity.Carousel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 轮播图数据访问接口
 */
@Mapper
public interface CarouselMapper {
    
    /**
     * 获取启用的轮播图列表，按排序字段排序
     */
    @Select("SELECT id, title, description, image, url, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM carousel WHERE status = 1 ORDER BY sort_order ASC, id ASC")
    List<Carousel> findActiveCarousels();

    /**
     * 根据ID查询轮播图
     */
    @Select("SELECT id, title, description, image, url, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM carousel WHERE id = #{id}")
    Carousel findById(Long id);

    /**
     * 获取所有轮播图
     */
    @Select("SELECT id, title, description, image, url, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM carousel ORDER BY sort_order ASC, id ASC")
    List<Carousel> findAll();
}
