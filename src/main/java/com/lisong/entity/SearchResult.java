package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;

/**
 * 搜索结果实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchResult {
    
    private Long id;
    private String title;
    private String poster;
    private BigDecimal rating;
    private Integer year;
    private String type; // movie, tv, variety
    
    /**
     * 电影搜索结果构造器
     */
    public static SearchResult fromMovie(Movie movie) {
        SearchResult result = new SearchResult();
        result.setId(movie.getId());
        result.setTitle(movie.getTitle());
        result.setPoster(movie.getPoster());
        result.setRating(movie.getRating());
        result.setYear(movie.getMovieYear());
        result.setType("movie");
        return result;
    }
    
    /**
     * 电视剧搜索结果构造器
     */
    public static SearchResult fromTvSeries(TvSeries tvSeries) {
        SearchResult result = new SearchResult();
        result.setId(tvSeries.getId());
        result.setTitle(tvSeries.getTitle());
        result.setPoster(tvSeries.getPoster());
        result.setRating(tvSeries.getRating());
        result.setYear(tvSeries.getSeriesYear());
        result.setType("tv");
        return result;
    }
    
    /**
     * 综艺节目搜索结果构造器
     */
    public static SearchResult fromVarietyShow(VarietyShow varietyShow) {
        SearchResult result = new SearchResult();
        result.setId(varietyShow.getId());
        result.setTitle(varietyShow.getTitle());
        result.setPoster(varietyShow.getPoster());
        result.setRating(varietyShow.getRating());
        result.setYear(varietyShow.getShowYear());
        result.setType("variety");
        return result;
    }
}
