package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 分页数据实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationData {
    
    private Integer page;
    private Integer limit;
    private Long total;
    private Integer totalPages;
    
    public PaginationData(Integer page, Integer limit, Long total) {
        this.page = page;
        this.limit = limit;
        this.total = total;
        this.totalPages = (int) Math.ceil((double) total / limit);
    }
}
