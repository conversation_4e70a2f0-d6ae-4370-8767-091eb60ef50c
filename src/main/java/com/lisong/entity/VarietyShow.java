package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 综艺节目实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VarietyShow {
    
    private Long id;
    private String title;
    private String poster;
    private String backdrop;
    private BigDecimal rating;
    private Integer showYear;
    private String genre; // JSON格式存储
    private String region;
    private String description;
    private String showStatus; // weekly, finished, special
    private String updateDay;
    private String updateTime;
    private Integer season;
    private String hosts; // JSON格式存储
    private String guests; // JSON格式存储
    private LocalDate firstAirDate;
    private Long viewCount;
    private Long likeCount;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 节目期数列表（用于详情接口）
    private List<VarietyShowEpisode> episodes;
    
    /**
     * 获取类型列表
     */
    @JsonProperty("genre")
    public List<String> getGenreList() {
        if (genre == null || genre.isEmpty()) {
            return List.of();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(genre, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return List.of();
        }
    }
    
    /**
     * 设置类型列表
     */
    public void setGenreList(List<String> genreList) {
        if (genreList == null || genreList.isEmpty()) {
            this.genre = "[]";
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.genre = mapper.writeValueAsString(genreList);
        } catch (Exception e) {
            this.genre = "[]";
        }
    }

    /**
     * 获取主持人列表
     */
    @JsonProperty("hosts")
    public List<String> getHostsList() {
        if (hosts == null || hosts.isEmpty()) {
            return List.of();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(hosts, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return List.of();
        }
    }

    /**
     * 设置主持人列表
     */
    public void setHostsList(List<String> hostsList) {
        if (hostsList == null || hostsList.isEmpty()) {
            this.hosts = "[]";
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.hosts = mapper.writeValueAsString(hostsList);
        } catch (Exception e) {
            this.hosts = "[]";
        }
    }
    
    /**
     * 获取嘉宾列表
     */
    @JsonProperty("guests")
    public List<String> getGuestsList() {
        if (guests == null || guests.isEmpty()) {
            return List.of();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(guests, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return List.of();
        }
    }

    /**
     * 设置嘉宾列表
     */
    public void setGuestsList(List<String> guestsList) {
        if (guestsList == null || guestsList.isEmpty()) {
            this.guests = "[]";
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.guests = mapper.writeValueAsString(guestsList);
        } catch (Exception e) {
            this.guests = "[]";
        }
    }
    
    /**
     * 获取年份（用于JSON响应）
     */
    @JsonProperty("year")
    public Integer getYear() {
        return this.showYear;
    }
    
    /**
     * 获取状态（用于JSON响应）
     */
    @JsonProperty("status")
    public String getShowStatusForJson() {
        return this.showStatus;
    }
}
