package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 电影实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Movie {
    
    private Long id;
    private String title;
    private String originalTitle;
    private String poster;
    private String backdrop;
    private BigDecimal rating;
    private Integer movieYear;
    private Integer duration;
    private String genre; // JSON格式存储
    private String region;
    private String director;
    private String cast; // JSON格式存储
    private String description;
    private LocalDate releaseDate;
    private String boxOffice;
    private String trailerUrl;
    private String playUrl;
    private String movieStatus;
    private Integer isHot;
    private Integer isLatest;
    private Long viewCount;
    private Long likeCount;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * 获取类型列表
     */
    @JsonProperty("genre")
    public List<String> getGenreList() {
        if (genre == null || genre.isEmpty()) {
            return List.of();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(genre, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return List.of();
        }
    }
    
    /**
     * 设置类型列表
     */
    public void setGenreList(List<String> genreList) {
        if (genreList == null || genreList.isEmpty()) {
            this.genre = "[]";
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.genre = mapper.writeValueAsString(genreList);
        } catch (Exception e) {
            this.genre = "[]";
        }
    }

    /**
     * 获取演员列表
     */
    @JsonProperty("cast")
    public List<String> getCastList() {
        if (cast == null || cast.isEmpty()) {
            return List.of();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(cast, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return List.of();
        }
    }

    /**
     * 设置演员列表
     */
    public void setCastList(List<String> castList) {
        if (castList == null || castList.isEmpty()) {
            this.cast = "[]";
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.cast = mapper.writeValueAsString(castList);
        } catch (Exception e) {
            this.cast = "[]";
        }
    }
}
