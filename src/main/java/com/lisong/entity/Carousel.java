package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

/**
 * 轮播图实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Carousel {
    
    private Long id;
    private String title;
    private String description;
    private String image;
    private String url;
    private Integer sortOrder;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
