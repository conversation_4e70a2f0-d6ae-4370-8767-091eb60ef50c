package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

/**
 * 电影分类实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MovieCategory {
    
    private String id;
    private String name;
    private String description;
    private String image;
    private String badge;
    private Integer sortOrder;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long count; // 该分类下的电影数量
}
