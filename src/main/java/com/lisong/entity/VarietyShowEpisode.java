package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 综艺节目期数实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VarietyShowEpisode {
    
    private Long id;
    private Long showId;
    private Integer episodeNumber;
    private String title;
    private LocalDate airDate;
    private Integer duration; // 时长（分钟）
    private String guests; // JSON格式存储本期嘉宾
    private String playUrl;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * 获取期数（用于JSON响应）
     */
    @JsonProperty("episode")
    public Integer getEpisode() {
        return this.episodeNumber;
    }
    
    /**
     * 获取本期嘉宾列表
     */
    @JsonProperty("guests")
    public List<String> getGuestsList() {
        if (guests == null || guests.isEmpty()) {
            return List.of();
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(guests, new TypeReference<List<String>>() {});
        } catch (Exception e) {
            return List.of();
        }
    }

    /**
     * 设置本期嘉宾列表
     */
    public void setGuestsList(List<String> guestsList) {
        if (guestsList == null || guestsList.isEmpty()) {
            this.guests = "[]";
            return;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            this.guests = mapper.writeValueAsString(guestsList);
        } catch (Exception e) {
            this.guests = "[]";
        }
    }
}
