package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 电视剧剧集实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TvSeriesEpisode {
    
    private Long id;
    private Long seriesId;
    private Integer episodeNumber;
    private String title;
    private Integer duration; // 时长（分钟）
    private String playUrl;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    /**
     * 获取集数（用于JSON响应）
     */
    @JsonProperty("episode")
    public Integer getEpisode() {
        return this.episodeNumber;
    }
}
