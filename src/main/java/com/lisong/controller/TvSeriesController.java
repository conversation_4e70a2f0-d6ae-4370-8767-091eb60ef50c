package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;
import com.lisong.service.TvSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 电视剧控制器
 */
@RestController
@RequestMapping("/tv-series")
public class TvSeriesController {
    
    @Autowired
    private TvSeriesService tvSeriesService;
    
    /**
     * 获取电视剧列表
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @param genre 类型筛选：drama,romance,comedy,action,thriller
     * @param region 地区筛选：cn,kr,us,jp,uk
     * @param status 状态筛选：ongoing,completed,upcoming
     * @param sort 排序方式：latest,rating,popular,name
     * @return 电视剧列表响应
     */
    @GetMapping
    public ApiResponse<TvSeriesListData> getTvSeriesList(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "genre", required = false) String genre,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "sort", required = false) String sort) {
        try {
            TvSeriesListData data = tvSeriesService.getTvSeriesList(page, limit, genre, region, status, sort);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取电视剧列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取电视剧详情
     * @param id 电视剧ID
     * @return 电视剧详情响应
     */
    @GetMapping("/{id}")
    public ApiResponse<TvSeries> getTvSeriesDetail(@PathVariable Long id) {
        try {
            TvSeries tvSeries = tvSeriesService.getTvSeriesDetail(id);
            if (tvSeries == null) {
                return ApiResponse.error(404, "电视剧不存在");
            }
            return ApiResponse.success(tvSeries);
        } catch (Exception e) {
            return ApiResponse.error("获取电视剧详情失败: " + e.getMessage());
        }
    }
}
