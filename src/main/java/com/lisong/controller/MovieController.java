package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.Movie;
import com.lisong.entity.MovieListData;
import com.lisong.entity.CategoryData;
import com.lisong.service.MovieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 电影控制器
 */
@RestController
@RequestMapping("/movies")
public class MovieController {
    
    @Autowired
    private MovieService movieService;
    
    /**
     * 获取最新电影列表
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @param genre 类型筛选
     * @param year 年份筛选
     * @param region 地区筛选
     * @param sort 排序方式
     * @return 电影列表响应
     */
    @GetMapping("/latest")
    public ApiResponse<MovieListData> getLatestMovies(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "genre", required = false) String genre,
            @RequestParam(value = "year", required = false) Integer year,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "sort", required = false) String sort) {
        try {
            MovieListData data = movieService.getLatestMovies(page, limit, genre, year, region, sort);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取最新电影列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取电影分类
     * @return 分类响应
     */
    @GetMapping("/categories")
    public ApiResponse<CategoryData> getMovieCategories() {
        try {
            CategoryData data = movieService.getMovieCategories();
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取电影分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取电影详情
     * @param id 电影ID
     * @return 电影详情响应
     */
    @GetMapping("/{id}")
    public ApiResponse<Movie> getMovieDetail(@PathVariable Long id) {
        try {
            Movie movie = movieService.getMovieDetail(id);
            if (movie == null) {
                return ApiResponse.error(404, "电影不存在");
            }
            return ApiResponse.success(movie);
        } catch (Exception e) {
            return ApiResponse.error("获取电影详情失败: " + e.getMessage());
        }
    }
}
