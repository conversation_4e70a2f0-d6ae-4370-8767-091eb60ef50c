package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.HomeData;
import com.lisong.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 首页控制器
 */
@RestController
@RequestMapping("/")
public class HomeController {
    
    @Autowired
    private HomeService homeService;
    
    /**
     * 获取首页数据
     * @return 首页数据响应
     */
    @GetMapping("/home")
    public ApiResponse<HomeData> getHomeData() {
        try {
            HomeData homeData = homeService.getHomeData();
            return ApiResponse.success(homeData);
        } catch (Exception e) {
            return ApiResponse.error("获取首页数据失败: " + e.getMessage());
        }
    }
}
