package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.VarietyShow;
import com.lisong.entity.VarietyShowListData;
import com.lisong.service.VarietyShowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 综艺节目控制器
 */
@RestController
@RequestMapping("/variety-shows")
public class VarietyShowController {
    
    @Autowired
    private VarietyShowService varietyShowService;
    
    /**
     * 获取综艺节目列表
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @param genre 类型筛选：reality,talk,music,comedy,competition
     * @param region 地区筛选：cn,kr,jp,us
     * @param status 状态筛选：weekly,finished,special
     * @param sort 排序方式：latest,rating,popular,name
     * @return 综艺节目列表响应
     */
    @GetMapping
    public ApiResponse<VarietyShowListData> getVarietyShowsList(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "genre", required = false) String genre,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "sort", required = false) String sort) {
        try {
            VarietyShowListData data = varietyShowService.getVarietyShowsList(page, limit, genre, region, status, sort);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取综艺节目列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取综艺节目详情
     * @param id 综艺节目ID
     * @return 综艺节目详情响应
     */
    @GetMapping("/{id}")
    public ApiResponse<VarietyShow> getVarietyShowDetail(@PathVariable Long id) {
        try {
            VarietyShow varietyShow = varietyShowService.getVarietyShowDetail(id);
            if (varietyShow == null) {
                return ApiResponse.error(404, "综艺节目不存在");
            }
            return ApiResponse.success(varietyShow);
        } catch (Exception e) {
            return ApiResponse.error("获取综艺节目详情失败: " + e.getMessage());
        }
    }
}
