package com.lisong.controller;

import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;
import com.lisong.service.TvSeriesService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 电视剧控制器测试
 */
@WebMvcTest(TvSeriesController.class)
public class TvSeriesControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TvSeriesService tvSeriesService;

    @Test
    public void testGetTvSeriesList() throws Exception {
        // Mock service response
        TvSeriesListData mockData = new TvSeriesListData();
        when(tvSeriesService.getTvSeriesList(any(), any(), any(), any(), any(), any()))
                .thenReturn(mockData);

        // Test the endpoint
        mockMvc.perform(get("/tv-series")
                        .param("page", "1")
                        .param("limit", "12"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));
    }

    @Test
    public void testGetTvSeriesDetail() throws Exception {
        // Mock service response
        TvSeries mockTvSeries = new TvSeries();
        mockTvSeries.setId(1L);
        mockTvSeries.setTitle("测试电视剧");
        when(tvSeriesService.getTvSeriesDetail(1L)).thenReturn(mockTvSeries);

        // Test the endpoint
        mockMvc.perform(get("/tv-series/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.title").value("测试电视剧"));
    }

    @Test
    public void testGetTvSeriesDetailNotFound() throws Exception {
        // Mock service response
        when(tvSeriesService.getTvSeriesDetail(999L)).thenReturn(null);

        // Test the endpoint
        mockMvc.perform(get("/tv-series/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("电视剧不存在"));
    }
}
