package com.lisong.controller;

import com.lisong.entity.VarietyShow;
import com.lisong.entity.VarietyShowListData;
import com.lisong.service.VarietyShowService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 综艺节目控制器测试
 */
@WebMvcTest(VarietyShowController.class)
public class VarietyShowControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private VarietyShowService varietyShowService;

    @Test
    public void testGetVarietyShowsList() throws Exception {
        // Mock service response
        VarietyShowListData mockData = new VarietyShowListData();
        when(varietyShowService.getVarietyShowsList(any(), any(), any(), any(), any(), any()))
                .thenReturn(mockData);

        // Test the endpoint
        mockMvc.perform(get("/variety-shows")
                        .param("page", "1")
                        .param("limit", "12"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"));
    }

    @Test
    public void testGetVarietyShowDetail() throws Exception {
        // Mock service response
        VarietyShow mockVarietyShow = new VarietyShow();
        mockVarietyShow.setId(1L);
        mockVarietyShow.setTitle("测试综艺节目");
        when(varietyShowService.getVarietyShowDetail(1L)).thenReturn(mockVarietyShow);

        // Test the endpoint
        mockMvc.perform(get("/variety-shows/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.title").value("测试综艺节目"));
    }

    @Test
    public void testGetVarietyShowDetailNotFound() throws Exception {
        // Mock service response
        when(varietyShowService.getVarietyShowDetail(999L)).thenReturn(null);

        // Test the endpoint
        mockMvc.perform(get("/variety-shows/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("综艺节目不存在"));
    }
}
