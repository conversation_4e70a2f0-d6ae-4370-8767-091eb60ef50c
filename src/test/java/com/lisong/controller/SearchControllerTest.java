package com.lisong.controller;

import com.lisong.service.SearchService;
import com.lisong.entity.SearchData;
import com.lisong.entity.SearchSuggestionData;
import com.lisong.entity.SearchResult;
import com.lisong.entity.PaginationData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 搜索控制器测试
 */
@WebMvcTest(SearchController.class)
public class SearchControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SearchService searchService;

    @Test
    public void testGlobalSearch() throws Exception {
        // 准备测试数据
        SearchResult movieResult = new SearchResult(1L, "阿凡达", "poster.jpg", new BigDecimal("8.5"), 2022, "movie");
        List<SearchResult> movies = Arrays.asList(movieResult);
        SearchData searchData = new SearchData(movies, Arrays.asList(), Arrays.asList(), 1, 10, 1);

        // Mock服务方法
        when(searchService.globalSearch(eq("阿凡达"), eq("all"), eq(1), eq(10)))
                .thenReturn(searchData);

        // 执行测试
        mockMvc.perform(get("/search")
                        .param("q", "阿凡达")
                        .param("type", "all")
                        .param("page", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.movies").isArray())
                .andExpect(jsonPath("$.data.movies[0].title").value("阿凡达"))
                .andExpect(jsonPath("$.data.movies[0].type").value("movie"));
    }

    @Test
    public void testSearchSuggestions() throws Exception {
        // 准备测试数据
        List<String> suggestions = Arrays.asList("阿凡达", "阿甘正传", "阿拉丁");
        SearchSuggestionData suggestionData = new SearchSuggestionData(suggestions);

        // Mock服务方法
        when(searchService.getSearchSuggestions(eq("阿"), eq(5)))
                .thenReturn(suggestionData);

        // 执行测试
        mockMvc.perform(get("/search/suggestions")
                        .param("q", "阿")
                        .param("limit", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.suggestions").isArray())
                .andExpect(jsonPath("$.data.suggestions[0]").value("阿凡达"));
    }

    @Test
    public void testSearchWithEmptyKeyword() throws Exception {
        // 测试空关键词
        mockMvc.perform(get("/search")
                        .param("q", "")
                        .param("type", "all"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.message").value("搜索关键词不能为空"));
    }

    @Test
    public void testSearchWithInvalidType() throws Exception {
        // 准备测试数据
        SearchData searchData = new SearchData(Arrays.asList(), Arrays.asList(), Arrays.asList(), 1, 10, 0);

        // Mock服务方法 - 当type无效时，会被设置为"all"
        when(searchService.globalSearch(eq("test"), eq("all"), eq(1), eq(10)))
                .thenReturn(searchData);

        // 执行测试
        mockMvc.perform(get("/search")
                        .param("q", "test")
                        .param("type", "invalid"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
