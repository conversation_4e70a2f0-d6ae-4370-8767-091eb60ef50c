# 搜索接口 API 文档

## 概述

本文档描述了基于Spring Boot四层架构和H2数据库实现的搜索接口功能，支持全局搜索电影、电视剧、综艺节目以及搜索建议功能。

## 技术架构

- **框架**: Spring Boot 3.4.6
- **数据库**: H2 (内存数据库)
- **ORM**: MyBatis
- **架构**: 四层架构 (Controller -> Service -> Mapper -> Entity)

## 接口列表

### 1. 全局搜索

**接口地址**: `GET /search`

**接口描述**: 全局搜索电影、电视剧、综艺节目

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| q | string | 是 | 搜索关键词 |
| type | string | 否 | 搜索类型：all,movie,tv,variety，默认all |
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10，最大50 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "movies": [
      {
        "id": 1,
        "title": "阿凡达：水之道",
        "poster": "https://example.com/poster1.jpg",
        "rating": 8.5,
        "year": 2022,
        "type": "movie"
      }
    ],
    "tvShows": [
      {
        "id": 1,
        "title": "狂飙",
        "poster": "https://example.com/tv1.jpg",
        "rating": 9.1,
        "year": 2023,
        "type": "tv"
      }
    ],
    "varietyShows": [
      {
        "id": 1,
        "title": "向往的生活",
        "poster": "https://example.com/variety1.jpg",
        "rating": 9.2,
        "year": 2024,
        "type": "variety"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 2. 搜索建议

**接口地址**: `GET /search/suggestions`

**接口描述**: 获取搜索建议

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| q | string | 是 | 搜索关键词 |
| limit | int | 否 | 建议数量，默认5，最大20 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "suggestions": [
      "阿凡达",
      "阿凡达：水之道",
      "阿甘正传",
      "阿拉丁",
      "阿基拉"
    ]
  }
}
```

## 搜索功能特性

### 1. 多类型搜索
- **全类型搜索** (type=all): 同时搜索电影、电视剧、综艺节目
- **单类型搜索**: 
  - 电影 (type=movie)
  - 电视剧 (type=tv)
  - 综艺节目 (type=variety)

### 2. 搜索字段
- **电影**: 标题、原标题、导演、演员
- **电视剧**: 标题、导演、演员
- **综艺节目**: 标题、主持人、嘉宾

### 3. 排序规则
- 按评分降序排列
- 评分相同时按观看量降序排列

### 4. 分页支持
- 支持分页查询
- 返回总数和总页数信息

## 测试示例

### 1. 搜索包含"阿"的内容
```bash
curl -X GET "http://localhost:8080/search?q=%E9%98%BF&type=all&page=1&limit=10"
```

### 2. 只搜索电影
```bash
curl -X GET "http://localhost:8080/search?q=%E9%98%BF&type=movie&page=1&limit=10"
```

### 3. 搜索电视剧
```bash
curl -X GET "http://localhost:8080/search?q=%E7%8B%82&type=tv&page=1&limit=10"
```

### 4. 搜索综艺节目
```bash
curl -X GET "http://localhost:8080/search?q=%E5%90%91%E5%BE%80&type=variety&page=1&limit=10"
```

### 5. 获取搜索建议
```bash
curl -X GET "http://localhost:8080/search/suggestions?q=%E9%98%BF&limit=5"
```

## 数据库示例数据

系统已预置了丰富的测试数据，包括：

### 电影数据
- 阿凡达：水之道
- 流浪地球2
- 满江红
- 阿甘正传
- 阿基拉
- 蜘蛛侠：英雄无归
- 蝙蝠侠：黑暗骑士
- 钢铁侠
- 美国队长
- 等等...

### 电视剧数据
- 狂飙
- 三体
- 去有风的地方
- 梦华录
- 人世间
- 权力的游戏
- 绝命毒师
- 老友记
- 生活大爆炸
- 西部世界
- 等等...

### 综艺节目数据
- 向往的生活
- 奔跑吧
- 中国好声音
- 明星大侦探
- 乘风破浪的姐姐
- 天天向上
- 非诚勿扰
- 我是歌手
- 爸爸去哪儿
- 中国达人秀
- 等等...

## 错误处理

### 常见错误码
- **400**: 搜索关键词不能为空
- **500**: 服务器内部错误

### 参数验证
- 搜索关键词不能为空
- 页码最小为1
- 每页数量限制在1-50之间
- 建议数量限制在1-20之间
- 无效的搜索类型会自动设置为"all"

## 项目结构

```
src/main/java/com/lisong/
├── controller/
│   └── SearchController.java          # 搜索控制器
├── service/
│   ├── SearchService.java             # 搜索服务接口
│   └── impl/
│       └── SearchServiceImpl.java     # 搜索服务实现
├── mapper/
│   └── SearchMapper.java              # 搜索数据访问层
└── entity/
    ├── SearchResult.java              # 搜索结果实体
    ├── SearchData.java                # 搜索响应数据实体
    └── SearchSuggestionData.java      # 搜索建议响应数据实体
```

## 启动说明

1. 确保Java 17+环境
2. 运行 `mvn spring-boot:run`
3. 访问 `http://localhost:8080/search` 进行测试
4. 可访问 `http://localhost:8080/h2-console` 查看数据库内容

## 注意事项

1. 中文搜索需要进行URL编码
2. H2数据库中的`cast`字段需要用双引号包围（因为是保留字）
3. 搜索建议去重处理，避免重复结果
4. 全类型搜索时，每种类型分配相等的结果数量
