# 首页接口项目

基于Spring Boot四层架构和H2数据库实现的首页接口项目。

## 项目结构

```
src/
├── main/
│   ├── java/com/lisong/
│   │   ├── controller/          # 控制层
│   │   │   ├── HomeController.java
│   │   │   └── TestController.java
│   │   ├── service/             # 服务层
│   │   │   ├── HomeService.java
│   │   │   └── impl/
│   │   │       └── HomeServiceImpl.java
│   │   ├── mapper/              # 数据访问层
│   │   │   ├── CarouselMapper.java
│   │   │   ├── MovieMapper.java
│   │   │   └── StatsMapper.java
│   │   ├── entity/              # 实体层
│   │   │   ├── ApiResponse.java
│   │   │   ├── Carousel.java
│   │   │   ├── Movie.java
│   │   │   ├── HomeData.java
│   │   │   └── Stats.java
│   │   └── HtmlJavaApplication.java
│   └── resources/
│       ├── application.properties
│       ├── schema.sql           # 数据库表结构
│       └── data.sql            # 示例数据
└── test/
    └── java/com/lisong/
        └── controller/
            └── HomeControllerTest.java
```

## 技术栈

- **Spring Boot 3.4.6** - 主框架
- **MyBatis 3.0.4** - ORM框架
- **H2 Database** - 内存数据库
- **Lombok** - 简化Java代码
- **Jackson** - JSON处理
- **Maven** - 项目管理

## 数据库设计

### 表结构

1. **carousel** - 轮播图表
2. **movie** - 电影表
3. **tv_show** - 电视剧表
4. **variety_show** - 综艺节目表
5. **user** - 用户表

## API接口

### 1. 首页接口

#### 1.1 获取首页数据
**接口地址**: `GET /home`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "carousel": [...],
    "hotMovies": [...],
    "latestMovies": [...],
    "stats": {
      "totalMovies": 11,
      "totalTVShows": 5,
      "totalVarietyShows": 5,
      "totalUsers": 5
    }
  }
}
```

### 2. 电影接口

#### 2.1 获取最新电影列表
**接口地址**: `GET /movies/latest`

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认12
- `genre` (string, 可选): 类型筛选
- `year` (int, 可选): 年份筛选
- `region` (string, 可选): 地区筛选 (us, cn, jp, kr)
- `sort` (string, 可选): 排序方式 (latest, rating, popular, name)

#### 2.2 获取电影分类
**接口地址**: `GET /movies/categories`

#### 2.3 获取电影详情
**接口地址**: `GET /movies/{id}`

### 3. 健康检查

**接口地址**: `GET /test/health`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "系统运行正常"
}
```

## 运行项目

### 1. 启动应用

```bash
./mvnw spring-boot:run
```

### 2. 访问接口

- 首页数据: http://localhost:8080/home
- 最新电影列表: http://localhost:8080/movies/latest
- 电影分类: http://localhost:8080/movies/categories
- 电影详情: http://localhost:8080/movies/1
- 健康检查: http://localhost:8080/test/health
- H2控制台: http://localhost:8080/h2-console

### 3. H2数据库连接信息

- **JDBC URL**: `jdbc:h2:mem:testdb`
- **用户名**: `sa`
- **密码**: `password`

## 运行测试

```bash
./mvnw test
```

## 示例数据

项目包含以下示例数据：
- 5个轮播图
- 11部电影（包含热门和最新标记，含即将上映电影）
- 5部电视剧
- 5个综艺节目
- 5个用户
- 12个电影分类

## 特性

- ✅ Spring Boot四层架构
- ✅ H2内存数据库
- ✅ MyBatis注解式SQL
- ✅ 统一响应格式
- ✅ JSON类型字段处理
- ✅ 自动数据初始化
- ✅ 单元测试支持
- ✅ H2控制台支持

## 开发说明

1. 所有实体类使用Lombok注解简化代码
2. 使用MyBatis注解方式编写SQL
3. JSON字段使用Jackson进行序列化/反序列化
4. 统一使用ApiResponse包装响应数据
5. H2数据库保留字使用双引号包围
