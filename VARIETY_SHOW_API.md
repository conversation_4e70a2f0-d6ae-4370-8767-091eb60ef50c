# 综艺节目API文档

## 概述

本文档描述了综艺节目模块的API接口，基于Spring Boot四层架构实现，使用H2数据库存储数据。

## 数据库设计

### 综艺节目表 (variety_show)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| title | VARCHAR(255) | 综艺节目标题 |
| poster | VARCHAR(500) | 海报图片URL |
| backdrop | VARCHAR(500) | 背景图片URL |
| rating | DECIMAL(3,1) | 评分 |
| show_year | INT | 年份 |
| genre | VARCHAR(500) | 类型（JSON格式） |
| region | VARCHAR(50) | 地区 |
| description | TEXT | 描述 |
| show_status | VARCHAR(50) | 状态：weekly/finished/special |
| update_day | VARCHAR(20) | 更新日期 |
| update_time | VARCHAR(10) | 更新时间 |
| season | INT | 季数 |
| hosts | TEXT | 主持人（JSON格式） |
| guests | TEXT | 常驻嘉宾（JSON格式） |
| first_air_date | DATE | 首播日期 |
| view_count | BIGINT | 观看次数 |
| like_count | BIGINT | 点赞次数 |
| status | TINYINT | 记录状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 综艺节目期数表 (variety_show_episode)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| show_id | BIGINT | 综艺节目ID |
| episode_number | INT | 期数 |
| title | VARCHAR(255) | 期标题 |
| air_date | DATE | 播出日期 |
| duration | INT | 时长（分钟） |
| guests | TEXT | 本期嘉宾（JSON格式） |
| play_url | VARCHAR(500) | 播放地址 |
| status | TINYINT | 记录状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## API接口

### 1. 获取综艺节目列表

**接口地址**: `GET /variety-shows`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认12 |
| genre | string | 否 | 类型筛选 |
| region | string | 否 | 地区筛选：cn,kr,jp,us |
| status | string | 否 | 状态筛选：weekly,finished,special |
| sort | string | 否 | 排序方式：latest,rating,popular,name |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "向往的生活",
        "poster": "https://example.com/variety1.jpg",
        "backdrop": "https://example.com/variety1_bg.jpg",
        "rating": 9.2,
        "year": 2024,
        "genre": ["真人秀", "生活", "治愈"],
        "region": "cn",
        "description": "明星嘉宾体验田园生活...",
        "status": "weekly",
        "updateDay": "周五",
        "updateTime": "22:00",
        "season": 8,
        "hosts": ["黄磊", "何炅"],
        "guests": ["张艺兴", "彭昱畅"],
        "firstAirDate": "2024-01-01",
        "viewCount": 3500000,
        "likeCount": 250000
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 12,
      "totalPages": 1
    }
  }
}
```

### 2. 获取综艺节目详情

**接口地址**: `GET /variety-shows/{id}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 综艺节目ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "向往的生活",
    "poster": "https://example.com/variety1.jpg",
    "backdrop": "https://example.com/variety1_bg.jpg",
    "rating": 9.2,
    "year": 2024,
    "genre": ["真人秀", "生活", "治愈"],
    "region": "cn",
    "description": "明星嘉宾体验田园生活，享受慢节奏的乡村时光...",
    "status": "weekly",
    "updateDay": "周五",
    "updateTime": "22:00",
    "season": 8,
    "hosts": ["黄磊", "何炅"],
    "guests": ["张艺兴", "彭昱畅"],
    "firstAirDate": "2024-01-01",
    "episodes": [
      {
        "episode": 1,
        "title": "第1期：新年第一餐",
        "airDate": "2024-01-05",
        "duration": 90,
        "guests": ["刘昊然", "关晓彤"],
        "playUrl": "https://example.com/variety1_ep1.m3u8"
      }
    ]
  }
}
```

## 示例数据

数据库中包含了12个综艺节目的示例数据，包括：
- 向往的生活（2024年，真人秀生活治愈，每周播出）
- 奔跑吧（2024年，真人秀竞技游戏，每周播出）
- 中国好声音（2024年，音乐选秀竞技，每周播出）
- 快乐大本营（2024年，娱乐访谈游戏，已完结）
- 极限挑战（2024年，真人秀冒险挑战，每周播出）
- 乘风破浪的姐姐（2024年，音乐竞技真人秀，已完结）
- 明星大侦探（2024年，推理悬疑真人秀，每周播出）
- 这就是街舞（2024年，舞蹈竞技音乐，已完结）
- 中餐厅（2024年，生活美食真人秀，已完结）
- 青春有你（2024年，选秀音乐舞蹈，特别节目）
- 密室大逃脱（2024年，推理冒险真人秀，每周播出）
- 王牌对王牌（2024年，游戏娱乐竞技，每周播出）

## 测试示例

```bash
# 获取综艺节目列表
curl -X GET "http://localhost:8080/variety-shows?page=1&limit=5"

# 获取综艺节目详情
curl -X GET "http://localhost:8080/variety-shows/1"

# 筛选每周播出的综艺节目，按评分排序
curl -X GET "http://localhost:8080/variety-shows?status=weekly&sort=rating"

# 筛选已完结的综艺节目，按热度排序
curl -X GET "http://localhost:8080/variety-shows?status=finished&sort=popular"

# 筛选特别节目
curl -X GET "http://localhost:8080/variety-shows?status=special"
```

## 技术架构

- **控制层**: VarietyShowController - 处理HTTP请求
- **服务层**: VarietyShowService/VarietyShowServiceImpl - 业务逻辑处理
- **数据访问层**: VarietyShowMapper/VarietyShowEpisodeMapper - 数据库操作
- **实体层**: VarietyShow/VarietyShowEpisode - 数据实体

## 特性

- 支持分页查询
- 支持多条件筛选（类型、地区、状态）
- 支持多种排序方式
- 包含期数详细信息
- JSON格式存储复杂数据（类型、主持人、嘉宾）
- 完整的错误处理
- 统一的响应格式
