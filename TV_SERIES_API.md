# 电视剧API文档

## 概述

本文档描述了电视剧模块的API接口，基于Spring Boot四层架构实现，使用H2数据库存储数据。

## 数据库设计

### 电视剧表 (tv_series)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| title | VARCHAR(255) | 电视剧标题 |
| poster | VARCHAR(500) | 海报图片URL |
| backdrop | VARCHAR(500) | 背景图片URL |
| rating | DECIMAL(3,1) | 评分 |
| series_year | INT | 年份 |
| genre | VARCHAR(500) | 类型（JSON格式） |
| region | VARCHAR(50) | 地区 |
| director | VARCHAR(255) | 导演 |
| cast | TEXT | 演员（JSON格式） |
| description | TEXT | 描述 |
| series_status | VARCHAR(50) | 状态：ongoing/completed/upcoming |
| current_episode | INT | 当前集数 |
| total_episodes | INT | 总集数 |
| update_day | VARCHAR(20) | 更新日期 |
| update_time | VARCHAR(10) | 更新时间 |
| first_air_date | DATE | 首播日期 |
| view_count | BIGINT | 观看次数 |
| like_count | BIGINT | 点赞次数 |
| status | TINYINT | 记录状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 剧集表 (tv_series_episode)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| series_id | BIGINT | 电视剧ID |
| episode_number | INT | 集数 |
| title | VARCHAR(255) | 集标题 |
| duration | INT | 时长（分钟） |
| play_url | VARCHAR(500) | 播放地址 |
| status | TINYINT | 记录状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## API接口

### 1. 获取电视剧列表

**接口地址**: `GET /tv-series`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认12 |
| genre | string | 否 | 类型筛选 |
| region | string | 否 | 地区筛选：cn,kr,us,jp,uk |
| status | string | 否 | 状态筛选：ongoing,completed,upcoming |
| sort | string | 否 | 排序方式：latest,rating,popular,name |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "狂飙",
        "poster": "https://example.com/tv1.jpg",
        "backdrop": "https://example.com/tv1_bg.jpg",
        "rating": 9.1,
        "year": 2023,
        "genre": ["剧情", "犯罪", "悬疑"],
        "region": "cn",
        "description": "一部反映扫黑除恶的现实主义力作...",
        "status": "completed",
        "currentEpisode": 39,
        "totalEpisodes": 39,
        "updateDay": "周五",
        "updateTime": "20:00",
        "firstAirDate": "2023-01-14",
        "viewCount": 2500000,
        "likeCount": 180000
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 10,
      "totalPages": 1
    }
  }
}
```

### 2. 获取电视剧详情

**接口地址**: `GET /tv-series/{id}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 电视剧ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "狂飙",
    "poster": "https://example.com/tv1.jpg",
    "backdrop": "https://example.com/tv1_bg.jpg",
    "rating": 9.1,
    "year": 2023,
    "genre": ["剧情", "犯罪", "悬疑"],
    "region": "cn",
    "director": "徐纪周",
    "cast": ["张译", "张颂文", "李一桐"],
    "description": "一部反映扫黑除恶的现实主义力作...",
    "status": "completed",
    "currentEpisode": 39,
    "totalEpisodes": 39,
    "updateDay": "周五",
    "updateTime": "20:00",
    "firstAirDate": "2023-01-14",
    "episodes": [
      {
        "episode": 1,
        "title": "第1集",
        "duration": 45,
        "playUrl": "https://example.com/tv1_ep1.m3u8"
      }
    ]
  }
}
```

## 示例数据

数据库中包含了10部电视剧的示例数据，包括：
- 狂飙（2023年，犯罪剧情）
- 三体（2023年，科幻剧情）
- 去有风的地方（2023年，都市治愈）
- 梦华录（2022年，古装爱情）
- 人世间（2022年，年代剧情）
- 庆余年2（2024年，古装喜剧，正在播出）
- 繁花（2023年，年代商战）
- 长月烬明（2023年，古装仙侠）
- 以爱为营（2023年，都市爱情）
- 莲花楼（2023年，古装武侠）

## 测试示例

```bash
# 获取电视剧列表
curl -X GET "http://localhost:8080/tv-series?page=1&limit=5"

# 获取电视剧详情
curl -X GET "http://localhost:8080/tv-series/1"

# 筛选已完结的电视剧，按评分排序
curl -X GET "http://localhost:8080/tv-series?status=completed&sort=rating"

# 筛选正在播出的电视剧
curl -X GET "http://localhost:8080/tv-series?status=ongoing"
```

## 技术架构

- **控制层**: TvSeriesController - 处理HTTP请求
- **服务层**: TvSeriesService/TvSeriesServiceImpl - 业务逻辑处理
- **数据访问层**: TvSeriesMapper/TvSeriesEpisodeMapper - 数据库操作
- **实体层**: TvSeries/TvSeriesEpisode - 数据实体

## 特性

- 支持分页查询
- 支持多条件筛选（类型、地区、状态）
- 支持多种排序方式
- 包含剧集详细信息
- JSON格式存储复杂数据（类型、演员）
- 完整的错误处理
- 统一的响应格式
